<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE X3D PUBLIC "ISO//Web3D//DTD X3D 4.0//EN" "http://www.web3d.org/specifications/x3d-4.0.dtd">
<X3D profile="Full" version="4.0"
     xmlns:xsd="http://www.w3.org/2001/XMLSchema-instance"
     xsd:noNamespaceSchemaLocation="http://www.web3d.org/specifications/x3d-4.0.xsd">
<head>
	<meta name="generator" content="castle-model-viewer, https://castle-engine.io/castle-model-viewer" />
	<meta name="source" content="minimized_connectors.x3d" />
</head>
<Scene>
	<ProtoDeclare name="RoundedRectangle2D">
		<ProtoInterface>
		</ProtoInterface>
		<ProtoBody>
			<IndexedFaceSet DEF="Geometry" />
			<IndexedFaceSet USE="Geometry" />
		</ProtoBody>
	</ProtoDeclare>
	<ProtoDeclare name="Widget">
		<ProtoInterface>
				<field accessType="inputOutput" type="SFNode" name="geometry" value="NULL" />
		</ProtoInterface>
		<ProtoBody>
			<Transform />
		</ProtoBody>
	</ProtoDeclare>
	<ProtoDeclare name="Node">
		<ProtoInterface>
		</ProtoInterface>
		<ProtoBody>
			<ProtoInstance name="Widget">
				<fieldValue name="geometry">
					<IndexedFaceSet />
				</fieldValue>
			</ProtoInstance>
		</ProtoBody>
	</ProtoDeclare>
	<ProtoInstance name="Node" />
</Scene>
</X3D>
